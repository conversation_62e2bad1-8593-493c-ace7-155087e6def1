version: '3.5'

services:
  postgres:
    container_name: nautilus-database
    image: postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-nautilus}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-pass}
      POSTGRES_DATABASE: nautilus
      PGDATA: /data/postgres
    volumes:
      - nautilus-database:/data/postgres
    ports:
      - "5432:5432"
    networks:
      - nautilus-network
    restart: unless-stopped

  pgadmin:
    container_name: nautilus-pgadmin
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD:-admin}
    volumes:
      - pgadmin:/root/.pgadmin
    security_opt:
      - no-new-privileges:true
    ports:
      - "${PGADMIN_PORT:-5051}:80"
    networks:
      - nautilus-network
    restart: unless-stopped

  redis:
    container_name: nautilus-redis
    image: redis
    ports:
      - 6379:6379
    restart: unless-stopped
    networks:
      - nautilus-network

networks:
  nautilus-network:

volumes:
  nautilus-database:
  pgadmin:
