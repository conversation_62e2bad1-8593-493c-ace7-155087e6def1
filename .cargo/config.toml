[target.'cfg(all())']
rustflags = [
    # https://rust-lang.github.io/rust-clippy/master/index.html#drop_non_drop
    # Disable this lint as we explicitly drop even when `Drop` is not implemented
    "-Aclippy::drop_non_drop",
]

[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-arg=-undefined",
    "-C", "link-arg=dynamic_lookup",
]

[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-arg=-undefined",
    "-C", "link-arg=dynamic_lookup",
]

[target.x86_64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static",
]
