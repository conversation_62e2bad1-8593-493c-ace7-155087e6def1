language = "C"
include_version = true
autogen_warning = "/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */"
includes = []
sys_includes = ["stdint.h", "Python.h"]
no_includes = true
tab_width = 4

[export.rename]
"bool" = "uint8_t"
"UnixNanos" = "uint64_t"
"TimedeltaNanos" = "int64_t"
"TimeEvent" = "TimeEvent_t"
"TimeEventHandler" = "TimeEventHandler_t"
"UUID4" = "UUID4_t"
"Logger" = "Logger_t"
"TraderId" = "TraderId_t"
"TestClock" = "TestClock_t"
