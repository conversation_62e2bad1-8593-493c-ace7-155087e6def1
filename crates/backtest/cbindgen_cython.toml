language = "Cython"
autogen_warning = "# Warning, this file is autogenerated by cbindgen. Don't modify this manually. */"
includes = []
sys_includes = ["stdint.h", "Python.h"]
no_includes = true
tab_width = 4

[cython]
header = '"../includes/backtest.h"'

[cython.cimports]
"libc.stdint" = [
    "uint8_t",
    "uint64_t",
    "uintptr_t",
]

"cpython.object" = [
    "PyObject",
]

"nautilus_trader.core.rust.common" = [
    "TestClock_API",
    "LiveClock_API",
]

"nautilus_trader.core.rust.core" = [
    "CVec",
    "UUID4_t",
]

[enum]
rename_variants = "ScreamingSnakeCase"

[export.rename]
"bool" = "bint"
"UnixNanos" = "uint64_t"
"TimedeltaNanos" = "int64_t"
"TimeEvent" = "TimeEvent_t"
"TimeEventHandler" = "TimeEventHandler_t"
"UUID4" = "UUID4_t"
"Logger" = "Logger_t"
"TestClock" = "TestClock_t"
