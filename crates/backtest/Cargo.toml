[package]
name = "nautilus-backtest"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_backtest"
crate-type = ["rlib", "staticlib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-execution/extension-module",
  "nautilus-model/extension-module",
]
ffi = [
  "cbindgen",
  "nautilus-core/ffi",
  "nautilus-common/ffi",
  "nautilus-execution/ffi",
  "nautilus-model/ffi",
]
python = [
  "pyo3",
  "nautilus-core/python",
  "nautilus-common/python",
  "nautilus-execution/python",
  "nautilus-model/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-data = { workspace = true }
nautilus-execution = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-persistence = { workspace = true }
nautilus-portfolio = { workspace = true }
nautilus-risk = { workspace = true }
nautilus-system = { workspace = true }

anyhow = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
rust_decimal = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
tempfile = { workspace = true }
rstest = { workspace = true }

[build-dependencies]
cbindgen = { workspace = true, optional = true }
