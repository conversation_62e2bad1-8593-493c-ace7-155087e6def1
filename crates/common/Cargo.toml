[package]
name = "nautilus-common"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_common"
crate-type = ["rlib", "staticlib"]

[features]
default = ["indicators", "rstest"]
extension-module = [
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-indicators/extension-module",
  "pyo3/extension-module",
]
ffi = [
  "nautilus-core/ffi",
  "nautilus-model/ffi",
  "cbindgen",
]
indicators = ["nautilus-indicators"]
python = [
  "nautilus-core/python",
  "nautilus-model/python",
  "pyo3",
  "pyo3-async-runtimes",
]
defi = [
  "nautilus-model/defi",
  "alloy-primitives",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-indicators = { workspace = true, optional = true }
nautilus-model = { workspace = true, features = ["stubs"] }

ahash = { workspace = true }
alloy-primitives = { workspace = true, optional = true }
anyhow = { workspace = true }
async-stream = { workspace = true }
async-trait = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
derive_builder = { workspace = true }
futures = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
pyo3-stub-gen = { workspace = true }
regex = { workspace = true }
rstest = { workspace = true, optional = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true }
sysinfo = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
ustr = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
proptest = { workspace = true }
rand = { workspace = true }
regex = { workspace = true }
tempfile = { workspace = true }

[build-dependencies]
cbindgen = { workspace = true, optional = true }

[[bench]]
name = "cache_orders"
path = "benches/cache/orders.rs"
harness = false

[[bench]]
name = "matching"
path = "benches/matching.rs"
harness = false

[[bench]]
name = "cache_query_sets"
path = "benches/cache/query_sets.rs"
harness = false
