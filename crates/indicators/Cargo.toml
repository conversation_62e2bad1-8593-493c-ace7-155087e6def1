[package]
name = "nautilus-indicators"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_indicators"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
]
python = [
  "pyo3",
  "nautilus-core/python",
  "nautilus-model/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }

anyhow = { workspace = true }
arraydeque = { workspace = true }
pyo3 = { workspace = true, optional = true }
strum = { workspace = true }

[dev-dependencies]
rstest = { workspace = true }
