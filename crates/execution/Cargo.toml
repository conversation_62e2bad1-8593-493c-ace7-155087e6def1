[package]
name = "nautilus-execution"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_execution"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
]
ffi = [
  "nautilus-common/ffi",
  "nautilus-core/ffi",
  "nautilus-model/ffi",
]
python = [
  "pyo3",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-model/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }

anyhow = { workspace = true }
chrono = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
rand = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
serde = { workspace = true }
ustr = { workspace = true }
uuid = { workspace = true }

criterion = { workspace = true }
rstest = { workspace = true }
