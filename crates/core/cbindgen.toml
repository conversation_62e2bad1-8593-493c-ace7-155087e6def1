language = "C"
include_version = true
autogen_warning = "/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */"
includes = []
sys_includes = ["stdint.h", "Python.h"]
no_includes = true
tab_width = 4

[export]
# A list of additional items to always include in the generated bindings if they're
# found but otherwise don't appear to be used by the public API.
#
# default: []
# Explicit export declaration needed. CVec is not returned from a function in this module.
include = ["CVec"]

[enum]
rename_variants = "ScreamingSnakeCase"

[export.rename]
"bool" = "uint8_t"
"Ustr" = "char*"
"UnixNanos" = "uint64_t"
"TimedeltaNanos" = "int64_t"
"TimeEvent" = "TimeEvent_t"
"UUID4" = "UUID4_t"
