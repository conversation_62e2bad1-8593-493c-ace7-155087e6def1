[package]
name = "nautilus-infrastructure"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_infrastructure"
crate-type = ["rlib", "cdylib"]

[features]
default = ["redis"]  # redis needed by `nautilus_trader` by default for now
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
]
python = [
  "pyo3",
  "pyo3-async-runtimes",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-model/python",
]
redis = ["dep:redis"]
postgres = ["dep:sqlx"]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-cryptography = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }

ahash = { workspace = true }
anyhow = { workspace = true }
async-stream = { workspace = true }
async-trait = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
derive_builder = { workspace = true }
futures = { workspace = true }
indexmap = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
log = { workspace = true }
rmp-serde = { workspace = true }
rust_decimal = { workspace = true }
semver = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
sqlx = { workspace = true, optional = true }
tokio = { workspace = true }
tracing = { workspace = true }
ustr = { workspace = true }
redis = { workspace = true, optional = true }

[dev-dependencies]
rstest = { workspace = true }
serde = { workspace = true }
