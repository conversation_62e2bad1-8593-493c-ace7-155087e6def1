[package]
name = "nautilus-cryptography"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_cryptography"
crate-type = ["rlib", "staticlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
]
python = [
  "pyo3",
  "nautilus-core/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true }

anyhow = { workspace = true }
base64 = { workspace = true }
ed25519-dalek = { workspace = true }
hex = { workspace = true }
aws-lc-rs = { workspace = true }
pyo3 = { workspace = true, optional = true }
rand = { workspace = true }
rustls = { workspace = true }
tracing = { workspace = true }
pem = { workspace = true }
webpki-roots = { workspace = true }

[dev-dependencies]
criterion = { workspace = true }
rstest = { workspace = true }
