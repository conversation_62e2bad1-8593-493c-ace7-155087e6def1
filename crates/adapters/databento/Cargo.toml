[package]
name = "nautilus-databento"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_databento"
crate-type = ["rlib", "cdylib"]

[features]
default = ["live"]
extension-module = [
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "pyo3/extension-module",
]
live = [
  "nautilus-live",
  "nautilus-system",
  "dotenvy",
  "tracing-subscriber",
]
python = [
  "nautilus-core/ffi",  # Temporary as python currently relies on the ffi CVec
  "nautilus-core/python",
  "nautilus-model/python",
  "pyo3",
  "pyo3-async-runtimes",
]
high-precision = [
  "nautilus-model/high-precision",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-data = { workspace = true }
nautilus-live = { workspace = true, optional = true }
nautilus-model = { workspace = true }
nautilus-system = { workspace = true, optional = true }

ahash = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
databento = { workspace = true }
dotenvy = { workspace = true, optional = true }
fallible-streaming-iterator = { workspace = true }
indexmap = { workspace = true }
itoa = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
serde = { workspace = true }
serde_json = { workspace = true }
strum = { workspace = true }
tokio = { workspace = true }
tokio-util = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, optional = true }
time = { workspace = true }
ustr = { workspace = true }

[dev-dependencies]
nautilus-testkit = { workspace = true }
rstest = { workspace = true }
tracing-test = { workspace = true }

[[bin]]
name = "databento-sandbox"
path = "bin/sandbox.rs"
required-features = ["python"]

[[bin]]
name = "databento-node-test"
path = "bin/node_test.rs"
required-features = ["live"]
