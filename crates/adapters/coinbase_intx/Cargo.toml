[package]
name = "nautilus-coinbase-intx"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_coinbase_intx"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-network/extension-module",
]
python = [
  "pyo3",
  "pyo3-async-runtimes",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-model/python",
  "nautilus-network/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-model = { workspace = true }
nautilus-network = { workspace = true }

anyhow = { workspace = true }
async-stream = { workspace = true }
base64 = { workspace = true }
chrono = { workspace = true }
derive_builder = { workspace = true }
futures-util = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
reqwest = { workspace = true }
rust_decimal = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_urlencoded = { workspace = true }
aws-lc-rs = { workspace = true }
strum = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-tungstenite = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }  # Needed for example binaries
ustr = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
nautilus-testkit = { workspace = true }
rstest = { workspace = true }
tracing-test = { workspace = true }

[[bin]]
name = "coinbase-intx-http-private"
path = "bin/http-private.rs"
required-features = ["python"]

[[bin]]
name = "coinbase-intx-http-public"
path = "bin/http-public.rs"
required-features = ["python"]

[[bin]]
name = "coinbase-intx-ws"
path = "bin/websocket.rs"
required-features = ["python"]
