[package]
name = "nautilus-hyperliquid"
readme = "README.md"
publish = false  # Do not publish to crates.io yet
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_hyperliquid"
crate-type = ["rlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-common/extension-module",
  "nautilus-core/extension-module",
  "nautilus-execution/extension-module",
  "nautilus-model/extension-module",
  "nautilus-network/extension-module",
]
python = [
  "pyo3",
  "pyo3-async-runtimes",
  "nautilus-common/python",
  "nautilus-core/python",
  "nautilus-execution/python",
  "nautilus-model/python",
  "nautilus-network/python",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-execution = { workspace = true }
nautilus-model = { workspace = true }
nautilus-network = { workspace = true }
nautilus-serialization = { workspace = true }

ahash = { workspace = true }
anyhow = { workspace = true }
async-stream = { workspace = true }
base64 = { workspace = true }
chrono = { workspace = true }
dashmap = { workspace = true }
derive_builder = { workspace = true }
futures-util = { workspace = true }
indexmap = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
pyo3-async-runtimes = { workspace = true, optional = true }
reqwest = { workspace = true }
aws-lc-rs = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_urlencoded = { workspace = true }
strum = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-tungstenite = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }  # Needed for example binaries
ustr = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
nautilus-testkit = { workspace = true }
criterion = { workspace = true }
rstest = { workspace = true }
tracing-test = { workspace = true }

[[bin]]
name = "hyperliquid-http-public"
path = "bin/http-public.rs"

[[bin]]
name = "hyperliquid-http-private"
path = "bin/http-private.rs"

[[bin]]
name = "hyperliquid-ws-data"
path = "bin/ws-data.rs"

[[bin]]
name = "hyperliquid-ws-exec"
path = "bin/ws-exec.rs"
