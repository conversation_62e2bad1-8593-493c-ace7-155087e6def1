[package]
name = "nautilus-cli"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-common = { workspace = true }
nautilus-core = { workspace = true }
nautilus-infrastructure = { workspace = true, features = ["postgres"] }
nautilus-model = { workspace = true }

anyhow = { workspace = true }
clap = { workspace = true }
dotenvy = { workspace = true }
log = { workspace = true }
simple_logger = { workspace = true }
tokio = { workspace = true }

[[bin]]
name = "nautilus"
path = "src/bin/cli.rs"
