# Pull Request

**NautilusTrader prioritizes correctness and reliability, please follow existing patterns for validation and testing.**

- [ ] I have reviewed the `CONTRIBUTING.md` and followed the established practices

## Summary

<!-- Provide a brief description of *what* changed, *why* it was changed, and the impact on the system or users (2–3 sentences). -->

## Related Issues/PRs

<!-- List any related GitHub issues or PRs (e.g., `Closes #123`, `Related to #456`). -->

## Type of change

<!-- Select all that apply. -->

- [ ] Bug fix (non-breaking)
- [ ] New feature (non-breaking)
- [ ] Breaking change (impacts existing behavior)
- [ ] Documentation update
- [ ] Maintenance / chore

## Breaking change details (if applicable)

<!-- If this is a breaking change, describe the impact and any migration steps required for users or developers. -->

## Documentation

- [ ] Documentation changes follow the style guide (`docs/developer_guide/docs.md`)

## Release notes

- [ ] I added a concise entry to `RELEASES.md` that follows the existing conventions (when applicable)

## Testing

**Ensure new or changed logic is covered by tests.**

- [ ] Affected code paths are already covered by the test suite
- [ ] I added/updated tests to cover new or changed logic

<!-- Briefly describe how the changes were tested (e.g., unit tests in `tests/unit/test_file.py`, or *additional* manual testing). -->
