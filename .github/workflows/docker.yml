name: docker

permissions: # Principle of least privilege
  contents: read
  actions: read
  packages: write # Required for pushing Docker images to GHCR

on:
  push:
    branches: [master, nightly]

jobs:
  build-docker-images:
    name: build-docker-images
    runs-on: ubuntu-latest
    env:
      BUILD_MODE: release
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Free disk space (Ubuntu)
        # https://github.com/jlumbroso/free-disk-space
        uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
        with:
          tool-cache: true
          android: false
          dotnet: false
          haskell: false
          large-packages: true
          docker-images: true
          swap-storage: true

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 1

      - name: Set up QEMU
        # https://github.com/docker/setup-qemu-action
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

      - name: Set up Docker Buildx
        # https://github.com/docker/setup-buildx-action
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Login to GHCR
        # https://github.com/docker/login-action
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.PACKAGES_TOKEN }}

      - name: Get branch name
        id: branch-name
        run: |
          echo "current_branch=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT

      - name: Build nautilus_trader image (nightly)
        if: ${{ steps.branch-name.outputs.current_branch == 'nightly' }}
        id: docker_build_trader_nightly
        # https://github.com/docker/build-push-action
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          file: ".docker/nautilus_trader.dockerfile"
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/nautilus_trader:nightly
          cache-from: type=gha
          cache-to: type=gha
      - name: Digest nautilus_trader image
        run: echo ${{ steps.docker_build_trader_nightly.outputs.digest }}

      - name: Build nautilus_trader image (latest)
        if: ${{ steps.branch-name.outputs.current_branch == 'master' }}
        id: docker_build_trader_latest
        # https://github.com/docker/build-push-action
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          file: ".docker/nautilus_trader.dockerfile"
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/nautilus_trader:latest
          cache-from: type=gha
          cache-to: type=gha
      - name: Digest nautilus_trader image
        run: echo ${{ steps.docker_build_trader_latest.outputs.digest }}

      - name: Build jupyterlab image (nightly)
        if: ${{ steps.branch-name.outputs.current_branch == 'nightly' }}
        id: docker_build_jupyterlab_nightly
        # https://github.com/docker/build-push-action
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          file: ".docker/jupyterlab.dockerfile"
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/jupyterlab:nightly
          cache-from: type=gha
          cache-to: type=gha
          build-args: |
            GIT_TAG=${{ steps.branch-name.outputs.current_branch }}
      - name: Digest jupyterlab image
        run: echo ${{ steps.docker_build_jupyterlab_nightly.outputs.digest }}

      - name: Build jupyterlab image (latest)
        if: ${{ steps.branch-name.outputs.current_branch == 'master' }}
        id: docker_build_jupyterlab_latest
        # https://github.com/docker/build-push-action
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          file: ".docker/jupyterlab.dockerfile"
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/jupyterlab:latest
          cache-from: type=gha
          cache-to: type=gha
          build-args: |
            GIT_TAG=latest

      - name: Digest jupyterlab image
        run: echo ${{ steps.docker_build_jupyterlab_latest.outputs.digest }}
