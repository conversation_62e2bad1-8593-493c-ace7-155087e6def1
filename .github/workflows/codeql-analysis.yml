name: codeql-analysis

permissions: # Principle of least privilege
  contents: read
  actions: read
  security-events: write # Required for CodeQL to upload SARIF

on:
  pull_request:
    branches: [master]
  schedule:
    - cron: '42 13 * * 4'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        language: ['python']
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 1

      - name: Initialize CodeQL
        # https://github.com/github/codeql-action
        uses: github/codeql-action/init@9b02dc2f60288b463e7a66e39c78829b62780db7 # codeql-bundle-v2.22.1
        with:
          languages: ${{ matrix.language }}
          # If you wish to specify custom queries, you can do so here or in a config file.
          # By default, queries listed here will override any specified in a config file.
          # Prefix the list here with "+" to use these queries and those in the config file.
          # queries: ./path/to/local/query, your-org/your-repo/queries@main

      - name: Perform CodeQL Analysis
        # https://github.com/github/codeql-action
        uses: github/codeql-action/analyze@9b02dc2f60288b463e7a66e39c78829b62780db7 # codeql-bundle-v2.22.1
