name: build-docs

permissions: # Principle of least privilege
  contents: read
  actions: read

on:
  push:
    branches: [master, nightly]

jobs:
  build-docs:
    runs-on: ubuntu-latest
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Fire event to nautilus_docs
        run: |
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.REPOSITORY_ACCESS_TOKEN }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/nautechsystems/nautilus_docs/dispatches \
            -d '{"event_type":"push"}'
