name: build-v2

# Build & publish wheels for the *v2* Python package living under the
# `python/` directory. These wheels are uploaded to the dedicated
# v2 package index at:
#   https://packages.nautechsystems.io/v2/simple/nautilus-trader/

permissions:
  contents: read
  actions: read

on:
  push:
    branches:
      - develop
      - nightly

env:
  PACKAGE_DIR: python

jobs:
  pre-commit:
    runs-on: ubuntu-22.04  # glibc 2.35 – larger runtime range
    steps:
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: "3.13"
          free-disk-space: "true"

      - name: Run pre-commit
        run: pre-commit run --all-files

  build:
    needs: pre-commit
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
        python-version: ["3.13"]
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        shell: bash
    env:
      BUILD_MODE: release
      RUST_BACKTRACE: 1
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DB: nautilus
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install Nautilus CLI and run init postgres
        run: |
          make install-cli
          nautilus database init --schema ${{ github.workspace }}/schema/sql
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USERNAME: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DATABASE: nautilus

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      - name: Run Rust tests
        run: make cargo-test

      # Update version for dev/nightly branches
      - name: Update version in pyproject.toml
        if: ${{ github.ref != 'refs/heads/master' }}
        working-directory: ${{ env.PACKAGE_DIR }}
        run: |
          bash ../scripts/ci/update-pyproject-version.sh

      # Build the wheel for v2 under python/ using maturin
      - name: Build wheel (v2)
        working-directory: ${{ env.PACKAGE_DIR }}
        run: |
          pip install --upgrade maturin
          maturin build --release --out ../dist

      - name: Upload wheel artifact
        uses: ./.github/actions/upload-artifact-wheel

  publish:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      actions: write # Required for deleting artifacts
      contents: read
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CLOUDFLARE_R2_URL: ${{ secrets.CLOUDFLARE_R2_URL }}
      CLOUDFLARE_R2_REGION: "auto"
      CLOUDFLARE_R2_BUCKET_NAME: "packages"
      CLOUDFLARE_R2_PREFIX: "v2/simple/nautilus-trader"
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Publish wheels to Cloudflare R2 (v2 bucket)
        uses: ./.github/actions/publish-wheels

      - name: Fetch and delete artifacts for current run
        shell: bash
        run: |
          bash ./scripts/ci/publish-wheels-delete-artifacts.sh
