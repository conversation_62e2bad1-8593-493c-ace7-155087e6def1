name: coverage

permissions: # Principle of least privilege
  contents: read
  actions: read

on:
  push:
    branches: [nightly]

jobs:
  build:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DB: nautilus
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      # https://github.com/step-security/harden-runner
      - uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - name: Checkout repository
        # https://github.com/actions/checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Common setup
        uses: ./.github/actions/common-setup
        with:
          python-version: "3.11"

      - name: Install Nautilus CLI and run init postgres
        run: |
          make install-cli
          nautilus database init --schema ${{ github.workspace }}/schema/sql
        env:
          POSTGRES_HOST: localhost
          POSTGRES_PORT: 5432
          POSTGRES_USERNAME: postgres
          POSTGRES_PASSWORD: pass
          POSTGRES_DATABASE: nautilus

      - name: Cached test data
        uses: ./.github/actions/common-test-data

      # TODO: Temporarily pause coverage due runner receiving shutdown signal (OOM/resources)
      # - name: Run tests with coverage
      #   run: bash scripts/test-coverage.sh

      # - name: Upload coverage report
      #   uses: codecov/codecov-action@v4
      #   with:
      #     # fail_ci_if_error: true  # leave commented until flakiness improves
      #     token: ${{ secrets.CODECOV_TOKEN }}
      #     verbose: true
