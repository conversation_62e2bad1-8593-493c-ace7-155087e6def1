name: common-test-data
description: Common test data caching

runs:
  using: "composite"
  steps:
      - name: Cached test data
        id: cached-testdata-large
        # https://github.com/actions/cache
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: tests/test_data/large
          key: ${{ runner.os }}-large-files-${{ hashFiles('tests/test_data/large/checksums.json') }}
          restore-keys: ${{ runner.os }}-large-files-
