name: common-setup
description: Common environment setup

inputs:
  python-version:
    description: The Python version to setup
    required: true
  free-disk-space:
    description: Free disk space
    required: false
    default: "false"

runs:
  using: "composite"
  steps:
    # > --------------------------------------------------
    # > OS
    - name: Free disk space (Ubuntu)
      if: inputs.free-disk-space == 'true' && runner.os == 'Linux'
      # https://github.com/jlumbroso/free-disk-space
      uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
      with:
        tool-cache: true
        android: true
        dotnet: true
        haskell: true
        large-packages: true
        docker-images: true
        swap-storage: true

    - name: Free disk space (Windows)
      if: inputs.free-disk-space == 'true' && runner.os == 'Windows'
      shell: bash
      run: |
        rm -rf "/c/Program Files/dotnet"
        rm -rf "/c/Program Files (x86)/Microsoft Visual Studio/2019"

    - name: Install runner dependencies
      if: runner.os == 'Linux'
      shell: bash
      run: |
        sudo apt-get update
        sudo apt-get install -y curl clang git make pkg-config
        sudo apt-get install -y python3-dev libpython3-dev
        sudo apt-get install -y capnproto libcapnp-dev

    # > --------------------------------------------------
    # > Rust
    # GitHub runners come with a pre-installed Rust toolchain, this step forces an update
    # to ensure we have the latest version before setting our specific toolchain.
    # setup-rust-toolchain should handle this with override: true, but doesn't seem to work.
    - name: Reset and update Rust toolchain
      shell: bash
      run: |
        if command -v rustup &> /dev/null; then
          rustup update --force
        fi

    - name: Get Rust toolchain version
      id: rust-toolchain
      shell: bash
      run: |
        echo "TOOLCHAIN=$(bash scripts/rust-toolchain.sh)" >> $GITHUB_ENV

    # https://github.com/actions-rust-lang/setup-rust-toolchain
    - name: Set up Rust toolchain
      uses: actions-rust-lang/setup-rust-toolchain@9d7e65c320fdb52dcd45ffaa68deb6c02c8754d9 # v1.12.0
      with:
        toolchain: ${{ env.TOOLCHAIN }}
        components: clippy,rustfmt
        override: true

    - name: Install cargo-nextest
      # https://github.com/taiki-e/install-action # v2.53.2
      uses: taiki-e/install-action@d12e869b89167df346dd0ff65da342d1fb1202fb
      with:
        tool: nextest

    # > --------------------------------------------------
    # > sccache
    - name: Set sccache env vars (common)
      shell: bash
      run: |
        echo "RUSTC_WRAPPER=sccache" >> $GITHUB_ENV

        # Based on GitHub Actions runner constraints
        # and Nautilus Trader uncompressed package final size (~1 GiB)
        echo "SCCACHE_CACHE_SIZE=4G" >> $GITHUB_ENV
        echo "SCCACHE_IDLE_TIMEOUT=0" >> $GITHUB_ENV
        echo "SCCACHE_DIRECT=true" >> $GITHUB_ENV
        echo "SCCACHE_CACHE_MULTIARCH=1" >> $GITHUB_ENV

        echo "CARGO_INCREMENTAL=0" >> $GITHUB_ENV

    - name: Set sccache env vars (non-Windows)
      if: runner.os != 'Windows'
      shell: bash
      run: |
        echo "SCCACHE_DIR=${{ github.workspace }}/.cache/sccache" >> $GITHUB_ENV
        echo "CC=sccache clang" >> $GITHUB_ENV
        echo "CXX=sccache clang++" >> $GITHUB_ENV

    - name: Set sccache env vars (Windows)
      if: runner.os == 'Windows'
      shell: bash
      run: |
        echo SCCACHE_DIR="C:\.cache\sccache" >> $GITHUB_ENV
        echo CMAKE_C_COMPILER_LAUNCHER=sccache >> $GITHUB_ENV
        echo CMAKE_CXX_COMPILER_LAUNCHER=sccache >> $GITHUB_ENV

    - name: Cached sccache
      id: cached-sccache
      # https://github.com/actions/cache
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: ${{ env.SCCACHE_DIR }}
        key: sccache-${{ runner.os }}-${{ github.workflow }}-${{ github.job }}-${{ hashFiles('**/Cargo.toml', '**/Cargo.lock', '**/uv.lock') }}
        restore-keys: |
          sccache-${{ runner.os }}-${{ github.workflow }}-${{ github.job }}-
          sccache-${{ runner.os }}-${{ github.workflow }}-
          sccache-${{ runner.os }}-

    - name: Run sccache
      # https://github.com/Mozilla-Actions/sccache-action
      uses: mozilla-actions/sccache-action@65101d47ea8028ed0c98a1cdea8dd9182e9b5133 # v0.0.8

    # > --------------------------------------------------
    # > Python
    - name: Set up Python environment
      # https://github.com/actions/setup-python
      uses: actions/setup-python@42375524e23c412d93fb67b49958b491fce71c38 # v5.4.0
      with:
        python-version: ${{ inputs.python-version }}

    - name: Get Python version
      shell: bash
      run: |
        echo "PYTHON_VERSION=$(bash scripts/python-version.sh)" >> $GITHUB_ENV

    - name: Cache Python site-packages
      id: cached-site-packages
      # https://github.com/actions/cache
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: ~/.local/lib/python${{ inputs.python-version }}/site-packages
        key: ${{ runner.os }}-${{ inputs.python-version }}-site-packages
        restore-keys: |
          ${{ runner.os }}-site-packages-

    - name: Install pre-commit
      shell: bash
      run: pip install pre-commit==4.2.0

    # > --------------------------------------------------
    # > UV
    - name: Get uv version from uv-version
      shell: bash
      run: |
        echo "UV_VERSION=$(cat uv-version)" >> $GITHUB_ENV

    - name: Install uv
      # https://github.com/astral-sh/setup-uv
      uses: astral-sh/setup-uv@0c5e2b8115b80b4c7c5ddf6ffdd634974642d182  # 5.4.1
      with:
        version: ${{ env.UV_VERSION }}

    - name: Set uv cache-dir
      shell: bash
      run: |
        echo "UV_CACHE_DIR=$(uv cache dir)" >> $GITHUB_ENV

    - name: Cached uv
      id: cached-uv
      # https://github.com/actions/cache
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: ${{ env.UV_CACHE_DIR }}
        key: ${{ runner.os }}-${{ env.PYTHON_VERSION }}-uv-${{ hashFiles('**/uv.lock') }}

    # > --------------------------------------------------
    # > pre-commit
    - name: Cached pre-commit
      id: cached-pre-commit
      # https://github.com/actions/cache
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      with:
        path: ~/.cache/pre-commit
        key: ${{ runner.os }}-${{ env.PYTHON_VERSION }}-pre-commit-${{ hashFiles('.pre-commit-config.yaml') }}
